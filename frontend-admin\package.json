{"name": "frontend-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3002", "build": "next build", "start": "next start --port 3002", "lint": "next lint", "bun:dev": "bun --bun next dev --turbopack --port 3002", "bun:build": "bun --bun run build", "bun:install": "bun install"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collection": "^1.1.6", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-context": "^1.1.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menu": "^2.1.14", "@radix-ui/react-primitive": "^2.1.2", "@radix-ui/react-roving-focus": "^1.1.9", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.509.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-page-tracker": "^0.3.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}